#include "ai/plugins/ui/frame_processor_editor_dialog.h"

#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QFontDatabase>
#include <QStandardPaths>
#include <QTextStream>

namespace ui {

FrameProcessorEditorDialog::FrameProcessorEditorDialog(std::shared_ptr<ai::PythonFrameProcessor> processor, QWidget* parent)
    : QDialog(parent), processor_(processor) {
    setWindowTitle(tr("帧处理脚本编辑器"));
    setMinimumSize(800, 600);

    // 获取脚本目录
    scriptDirectory_ = QString::fromStdString(processor_->get_script_directory());
    
    // 确保脚本目录存在
    QDir dir(scriptDirectory_);
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // 创建脚本选择区域
    QHBoxLayout* scriptSelectionLayout = new QHBoxLayout();
    QLabel* scriptLabel = new QLabel(tr("选择脚本:"), this);
    scriptComboBox_ = new QComboBox(this);
    scriptComboBox_->setMinimumWidth(300);
    
    loadButton_ = new QPushButton(tr("加载"), this);
    saveButton_ = new QPushButton(tr("保存"), this);
    reloadButton_ = new QPushButton(tr("重新加载"), this);
    newButton_ = new QPushButton(tr("新建"), this);

    scriptSelectionLayout->addWidget(scriptLabel);
    scriptSelectionLayout->addWidget(scriptComboBox_);
    scriptSelectionLayout->addWidget(loadButton_);
    scriptSelectionLayout->addWidget(saveButton_);
    scriptSelectionLayout->addWidget(reloadButton_);
    scriptSelectionLayout->addWidget(newButton_);
    scriptSelectionLayout->addStretch();

    mainLayout->addLayout(scriptSelectionLayout);

    // 创建脚本编辑器
    scriptEditor_ = new QTextEdit(this);
    
    // 设置等宽字体
    QFont font = QFontDatabase::systemFont(QFontDatabase::FixedFont);
    font.setPointSize(10);
    scriptEditor_->setFont(font);
    
    mainLayout->addWidget(scriptEditor_);

    // 创建状态标签
    statusLabel_ = new QLabel(this);
    mainLayout->addWidget(statusLabel_);

    // 创建按钮区域
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* closeButton = new QPushButton(tr("关闭"), this);
    buttonLayout->addStretch();
    buttonLayout->addWidget(closeButton);

    mainLayout->addLayout(buttonLayout);

    // 连接信号和槽
    connect(loadButton_, &QPushButton::clicked, this, &FrameProcessorEditorDialog::loadScript);
    connect(saveButton_, &QPushButton::clicked, this, &FrameProcessorEditorDialog::saveScript);
    connect(reloadButton_, &QPushButton::clicked, this, &FrameProcessorEditorDialog::reloadScript);
    connect(newButton_, &QPushButton::clicked, this, &FrameProcessorEditorDialog::createNewScript);
    connect(closeButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(scriptComboBox_, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &FrameProcessorEditorDialog::scriptSelectionChanged);

    // 初始化脚本列表
    updateScriptList();
    
    // 加载当前脚本
    currentScriptPath_ = QString::fromStdString(processor_->get_script_path());
    if (!currentScriptPath_.isEmpty() && QFile::exists(currentScriptPath_)) {
        loadScriptContent(currentScriptPath_);
        
        // 设置当前选中的脚本
        int index = scriptComboBox_->findText(QFileInfo(currentScriptPath_).fileName());
        if (index >= 0) {
            scriptComboBox_->setCurrentIndex(index);
        }
    }
}

void FrameProcessorEditorDialog::updateScriptList() {
    scriptComboBox_->clear();
    
    QDir dir(scriptDirectory_);
    QStringList filters;
    filters << "*.py";
    dir.setNameFilters(filters);
    
    QStringList scriptFiles = dir.entryList(filters, QDir::Files, QDir::Name);
    scriptComboBox_->addItems(scriptFiles);
}

void FrameProcessorEditorDialog::loadScriptContent(const QString& filePath) {
    QFile file(filePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream in(&file);
        in.setCodec("UTF-8");
        scriptEditor_->setPlainText(in.readAll());
        file.close();
        
        currentScriptPath_ = filePath;
        statusLabel_->setText(tr("已加载脚本: %1").arg(filePath));
    } else {
        statusLabel_->setText(tr("无法打开脚本文件: %1").arg(filePath));
    }
}

void FrameProcessorEditorDialog::loadScript() {
    QString filePath = QFileDialog::getOpenFileName(
        this,
        tr("选择Python脚本"),
        scriptDirectory_,
        tr("Python Files (*.py)")
    );

    if (!filePath.isEmpty()) {
        loadScriptContent(filePath);
        
        // 更新脚本列表
        updateScriptList();
        
        // 设置当前选中的脚本
        int index = scriptComboBox_->findText(QFileInfo(filePath).fileName());
        if (index >= 0) {
            scriptComboBox_->setCurrentIndex(index);
        }
    }
}

void FrameProcessorEditorDialog::saveScript() {
    if (currentScriptPath_.isEmpty()) {
        // 如果没有当前脚本路径，则执行另存为操作
        QString filePath = QFileDialog::getSaveFileName(
            this,
            tr("保存Python脚本"),
            scriptDirectory_,
            tr("Python Files (*.py)")
        );

        if (!filePath.isEmpty()) {
            currentScriptPath_ = filePath;
        } else {
            return;
        }
    }

    QFile file(currentScriptPath_);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        out << scriptEditor_->toPlainText();
        file.close();
        
        statusLabel_->setText(tr("脚本已保存: %1").arg(currentScriptPath_));
        
        // 更新脚本列表
        updateScriptList();
        
        // 设置当前选中的脚本
        int index = scriptComboBox_->findText(QFileInfo(currentScriptPath_).fileName());
        if (index >= 0) {
            scriptComboBox_->setCurrentIndex(index);
        }
    } else {
        statusLabel_->setText(tr("无法保存脚本文件: %1").arg(currentScriptPath_));
    }
}

void FrameProcessorEditorDialog::reloadScript() {
    // 先保存当前脚本
    saveScript();
    
    // 然后重新加载脚本
    if (processor_->load_script(currentScriptPath_.toStdString())) {
        statusLabel_->setText(tr("脚本已重新加载: %1").arg(currentScriptPath_));
        QMessageBox::information(this, tr("成功"), tr("脚本已重新加载。"));
    } else {
        statusLabel_->setText(tr("重新加载脚本失败: %1").arg(QString::fromStdString(processor_->get_error_message())));
        QMessageBox::warning(this, tr("错误"),
            tr("重新加载失败: %1").arg(QString::fromStdString(processor_->get_error_message())));
    }
}

void FrameProcessorEditorDialog::createNewScript() {
    // 创建新脚本文件名
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString newFileName = QString("frame_processor_%1.py").arg(timestamp);
    QString newFilePath = QDir(scriptDirectory_).filePath(newFileName);
    
    // 创建新脚本内容
    QString templateContent = R"(# -*- coding: utf-8 -*-
"""
自定义帧处理脚本
创建时间: %1
"""

import numpy as np
import cv2

# 全局变量
params = {}
required_frames = 5  # 默认需要5帧

def initialize(parameters):
    """初始化函数"""
    global params, required_frames
    params = parameters
    
    # 从参数中读取需要的帧数（如果有）
    if 'required_frames' in parameters:
        try:
            required_frames = int(parameters['required_frames'])
            # 确保帧数在合理范围内
            if required_frames < 1:
                required_frames = 1
            elif required_frames > 100:
                required_frames = 100
        except:
            required_frames = 5  # 如果转换失败，使用默认值
    
    # 从参数中读取处理模式
    if 'mode' not in parameters:
        params['mode'] = 'original'  # 默认使用原始模式
    
    print(f"帧处理脚本初始化，需要帧数: {required_frames}，处理模式: {params['mode']}")

def get_required_frames():
    """返回脚本需要处理的帧数"""
    global required_frames
    return required_frames

def process_frames(frames):
    """处理多帧图像，返回一个处理后的图像"""
    global params
    
    # 打印收到的帧数
    print(f"收到 {len(frames)} 帧进行处理")
    
    # 如果没有帧，返回空图像
    if len(frames) == 0:
        return np.zeros((100, 100, 3), dtype=np.uint8)
    
    # 如果只有一帧，直接返回
    if len(frames) == 1:
        return frames[0]
    
    # 获取处理模式
    mode = params.get('mode', 'original')
    
    # 根据不同模式处理帧
    if mode == 'original':
        # 原始模式：直接返回当前帧
        return frames[0]
    
    elif mode == 'grayscale':
        # 灰度模式：转换为灰度图
        gray = cv2.cvtColor(frames[0], cv2.COLOR_BGR2GRAY)
        return cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
    
    elif mode == 'blur':
        # 模糊模式
        kernel_size = int(params.get('kernel_size', '5'))
        return cv2.GaussianBlur(frames[0], (kernel_size, kernel_size), 0)
    
    else:
        # 默认返回第一帧
        return frames[0]
)";

    // 替换时间戳
    templateContent = templateContent.arg(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));
    
    // 保存新脚本
    QFile file(newFilePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        out << templateContent;
        file.close();
        
        // 加载新脚本
        loadScriptContent(newFilePath);
        
        // 更新脚本列表
        updateScriptList();
        
        // 设置当前选中的脚本
        int index = scriptComboBox_->findText(QFileInfo(newFilePath).fileName());
        if (index >= 0) {
            scriptComboBox_->setCurrentIndex(index);
        }
        
        statusLabel_->setText(tr("已创建新脚本: %1").arg(newFilePath));
    } else {
        statusLabel_->setText(tr("无法创建新脚本: %1").arg(newFilePath));
    }
}

void FrameProcessorEditorDialog::scriptSelectionChanged(int index) {
    if (index >= 0) {
        QString scriptName = scriptComboBox_->itemText(index);
        QString scriptPath = QDir(scriptDirectory_).filePath(scriptName);
        
        // 加载选中的脚本
        loadScriptContent(scriptPath);
    }
}

} // namespace ui
