#pragma once

#include <QDialog>
#include <QComboBox>
#include <QCheckBox>
#include <QLabel>
#include <QPushButton>
#include <QTableWidget>
#include <memory>

#include "ai/cpp_frame_processor.h"
#include "../../../aivideocore_export.h"

namespace ai {
namespace plugins {
namespace ui {

/**
 * @brief C++帧处理器对话框
 */
class AIVIDEOCORE_API CppFrameProcessorDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param processor C++帧处理器
     * @param parent 父窗口
     */
    CppFrameProcessorDialog(std::shared_ptr<ai::CppFrameProcessor> processor, QWidget* parent = nullptr);

private slots:
    /**
     * @brief 应用设置
     */
    void applySettings();

    /**
     * @brief 取消设置
     */
    void cancelSettings();

    /**
     * @brief 插件选择改变
     * @param index 索引
     */
    void onPluginSelectionChanged(int index);

    /**
     * @brief 启用状态改变
     * @param state 状态
     */
    void onEnabledStateChanged(int state);

    /**
     * @brief 参数值改变
     * @param row 行
     * @param column 列
     */
    void onParameterValueChanged(int row, int column);

private:
    /**
     * @brief 更新参数表格
     */
    void updateParametersTable();

    /**
     * @brief 更新插件信息
     */
    void updatePluginInfo();

    std::shared_ptr<ai::CppFrameProcessor> processor_; ///< C++帧处理器
    QComboBox* pluginComboBox_; ///< 插件选择下拉框
    QCheckBox* enabledCheckBox_; ///< 启用复选框
    QTableWidget* parametersTable_; ///< 参数表格
    QLabel* descriptionLabel_; ///< 描述标签
    QLabel* versionLabel_; ///< 版本标签
    QLabel* authorLabel_; ///< 作者标签
    QLabel* requiredFramesLabel_; ///< 需要的帧数标签
    QPushButton* applyButton_; ///< 应用按钮
    QPushButton* cancelButton_; ///< 取消按钮

    std::map<std::string, std::string> currentParams_; ///< 当前参数
    bool isEnabled_; ///< 是否启用
    std::string currentPluginName_; ///< 当前插件名称
};

} // namespace ui
} // namespace plugins
} // namespace ai
