#include "ai/plugins/ui/python_frame_processor_dialog.h"

#include <QFileDialog>
#include <QFormLayout>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QHeaderView>
#include <QLabel>
#include <QMessageBox>
#include <QVBoxLayout>

#include "ai/plugins/ui/frame_processor_editor_dialog.h"

namespace ui {

PythonFrameProcessorDialog::PythonFrameProcessorDialog(std::shared_ptr<ai::PythonFrameProcessor> processor, QWidget* parent)
    : QDialog(parent), processor_(processor) {
    setWindowTitle(tr("Python帧处理器配置"));
    setMinimumSize(600, 500);

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // 创建脚本路径区域
    QGroupBox* scriptGroup = new QGroupBox(tr("脚本路径"), this);
    QHBoxLayout* scriptLayout = new QHBoxLayout(scriptGroup);

    scriptPathEdit_ = new QLineEdit(QString::fromStdString(processor_->get_script_path()), scriptGroup);
    scriptPathEdit_->setReadOnly(true);

    browseButton_ = new QPushButton(tr("浏览"), scriptGroup);
    reloadButton_ = new QPushButton(tr("重新加载"), scriptGroup);
    editButton_ = new QPushButton(tr("编辑脚本"), scriptGroup);

    scriptLayout->addWidget(scriptPathEdit_);
    scriptLayout->addWidget(browseButton_);
    scriptLayout->addWidget(reloadButton_);
    scriptLayout->addWidget(editButton_);

    mainLayout->addWidget(scriptGroup);

    // 创建启用复选框
    enableCheckBox_ = new QCheckBox(tr("启用Python帧处理"), this);
    enableCheckBox_->setChecked(processor_->is_enabled());
    mainLayout->addWidget(enableCheckBox_);

    // 创建参数区域
    QGroupBox* paramsGroup = new QGroupBox(tr("脚本参数"), this);
    QVBoxLayout* paramsLayout = new QVBoxLayout(paramsGroup);

    paramsTable_ = new QTableWidget(0, 2, paramsGroup);
    paramsTable_->setHorizontalHeaderLabels({tr("参数名"), tr("参数值")});
    paramsTable_->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    paramsTable_->setSelectionBehavior(QAbstractItemView::SelectRows);

    QHBoxLayout* paramsButtonLayout = new QHBoxLayout();
    addParamButton_ = new QPushButton(tr("添加"), paramsGroup);
    deleteParamButton_ = new QPushButton(tr("删除"), paramsGroup);
    applyButton_ = new QPushButton(tr("应用"), paramsGroup);

    paramsButtonLayout->addWidget(addParamButton_);
    paramsButtonLayout->addWidget(deleteParamButton_);
    paramsButtonLayout->addStretch();
    paramsButtonLayout->addWidget(applyButton_);

    paramsLayout->addWidget(paramsTable_);
    paramsLayout->addLayout(paramsButtonLayout);

    mainLayout->addWidget(paramsGroup);

    // 创建状态区域
    QGroupBox* statusGroup = new QGroupBox(tr("状态信息"), this);
    QVBoxLayout* statusLayout = new QVBoxLayout(statusGroup);

    statusText_ = new QTextEdit(statusGroup);
    statusText_->setReadOnly(true);
    statusLayout->addWidget(statusText_);

    mainLayout->addWidget(statusGroup);

    // 创建按钮区域
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* closeButton = new QPushButton(tr("关闭"), this);
    buttonLayout->addStretch();
    buttonLayout->addWidget(closeButton);

    mainLayout->addLayout(buttonLayout);

    // 连接信号和槽
    connect(browseButton_, &QPushButton::clicked, this, &PythonFrameProcessorDialog::browseScript);
    connect(reloadButton_, &QPushButton::clicked, this, &PythonFrameProcessorDialog::reloadScript);
    connect(editButton_, &QPushButton::clicked, this, &PythonFrameProcessorDialog::openScriptEditor);
    connect(addParamButton_, &QPushButton::clicked, this, &PythonFrameProcessorDialog::addParam);
    connect(deleteParamButton_, &QPushButton::clicked, this, &PythonFrameProcessorDialog::deleteParam);
    connect(applyButton_, &QPushButton::clicked, this, &PythonFrameProcessorDialog::applyParams);
    connect(enableCheckBox_, &QCheckBox::toggled, this, &PythonFrameProcessorDialog::enableProcessor);
    connect(closeButton, &QPushButton::clicked, this, &QDialog::accept);

    // 初始化参数表格
    updateParamsTable();
    updateStatus();
}

void PythonFrameProcessorDialog::browseScript() {
    QString filePath = QFileDialog::getOpenFileName(
        this,
        tr("选择Python脚本"),
        QString::fromStdString(processor_->get_script_directory()),
        tr("Python Files (*.py)")
    );

    if (!filePath.isEmpty()) {
        if (processor_->set_script_path(filePath.toStdString())) {
            scriptPathEdit_->setText(filePath);
            updateStatus();
            QMessageBox::information(this, tr("成功"), tr("脚本加载成功。"));
        } else {
            QMessageBox::warning(this, tr("错误"),
                tr("加载脚本失败: %1").arg(QString::fromStdString(processor_->get_error_message())));
        }
    }
}

void PythonFrameProcessorDialog::reloadScript() {
    if (processor_->reload_script()) {
        updateStatus();
        // 保存配置
        processor_->save_config();
        QMessageBox::information(this, tr("成功"), tr("脚本已重新加载。"));
    } else {
        QMessageBox::warning(this, tr("错误"),
            tr("重新加载失败: %1").arg(QString::fromStdString(processor_->get_error_message())));
    }
}

void PythonFrameProcessorDialog::addParam() {
    int row = paramsTable_->rowCount();
    paramsTable_->insertRow(row);
    paramsTable_->setItem(row, 0, new QTableWidgetItem(""));
    paramsTable_->setItem(row, 1, new QTableWidgetItem(""));
}

void PythonFrameProcessorDialog::deleteParam() {
    QList<QTableWidgetItem*> selectedItems = paramsTable_->selectedItems();
    if (!selectedItems.isEmpty()) {
        int row = selectedItems.first()->row();
        paramsTable_->removeRow(row);
    }
}

void PythonFrameProcessorDialog::applyParams() {
    std::map<std::string, std::string> params;
    for (int row = 0; row < paramsTable_->rowCount(); ++row) {
        QTableWidgetItem* keyItem = paramsTable_->item(row, 0);
        QTableWidgetItem* valueItem = paramsTable_->item(row, 1);
        if (keyItem && valueItem && !keyItem->text().isEmpty()) {
            params[keyItem->text().toStdString()] = valueItem->text().toStdString();
        }
    }

    processor_->set_params(params);
    updateStatus();
    // 保存配置
    processor_->save_config();
    QMessageBox::information(this, tr("成功"), tr("参数已应用。"));
}

void PythonFrameProcessorDialog::enableProcessor(bool enabled) {
    processor_->set_enabled(enabled);
    updateStatus();
    // 保存配置
    processor_->save_config();
}

void PythonFrameProcessorDialog::updateParamsTable() {
    paramsTable_->setRowCount(0);
    auto params = processor_->get_params();
    for (const auto& [key, value] : params) {
        int row = paramsTable_->rowCount();
        paramsTable_->insertRow(row);
        paramsTable_->setItem(row, 0, new QTableWidgetItem(QString::fromStdString(key)));
        paramsTable_->setItem(row, 1, new QTableWidgetItem(QString::fromStdString(value)));
    }
}

void PythonFrameProcessorDialog::updateStatus() {
    QString status;
    status += tr("脚本路径: %1\n").arg(QString::fromStdString(processor_->get_script_path()));
    status += tr("状态: %1\n").arg(processor_->is_enabled() ? tr("已启用") : tr("已禁用"));
    status += tr("需要帧数: %1\n").arg(processor_->get_required_frames());

    if (!processor_->get_error_message().empty()) {
        status += tr("错误信息: %1\n").arg(QString::fromStdString(processor_->get_error_message()));
    }

    statusText_->setText(status);

    // 根据是否有脚本路径来启用编辑按钮
    editButton_->setEnabled(!processor_->get_script_path().empty());
}

void PythonFrameProcessorDialog::openScriptEditor() {
    // 创建帧处理脚本编辑器对话框
    FrameProcessorEditorDialog editorDialog(processor_, this);

    // 显示对话框
    if (editorDialog.exec() == QDialog::Accepted) {
        // 如果脚本发生变化，更新状态
        updateStatus();
    }
}

} // namespace ui
