#include "ai/plugins/ui/python_script_dialog.h"

#include <Python.h>
#include <QApplication>
#include <QFileDialog>
#include <QInputDialog>
#include <QMessageBox>
#include <QStyle>

namespace ui {

PythonScriptDialog::PythonScriptDialog(std::shared_ptr<ai::AiProcessor> processor, QWidget* parent)
    : QDialog(parent), processor_(processor), script_manager_("scripts/task_plugins") {
    setWindowTitle(tr("Python 脚本管理"));
    setMinimumSize(600, 400);

    initUI();
    refreshScriptList();
}

PythonScriptDialog::~PythonScriptDialog() {
}

void PythonScriptDialog::initUI() {
    mainLayout_ = new QVBoxLayout(this);

    // 创建脚本列表
    QLabel* listLabel = new QLabel(tr("可用脚本:"), this);
    scriptListWidget_ = new QListWidget(this);
    scriptListWidget_->setSelectionMode(QAbstractItemView::SingleSelection);
    scriptListWidget_->setContextMenuPolicy(Qt::CustomContextMenu);

    connect(scriptListWidget_, &QListWidget::itemDoubleClicked, this, &PythonScriptDialog::onScriptItemDoubleClicked);
    connect(scriptListWidget_, &QListWidget::customContextMenuRequested, this, &PythonScriptDialog::onScriptListContextMenu);

    // 创建按钮布局
    QHBoxLayout* buttonLayout = new QHBoxLayout();

    newButton_ = new QPushButton(tr("新建脚本"), this);
    loadButton_ = new QPushButton(tr("加载脚本"), this);
    configButton_ = new QPushButton(tr("配置脚本"), this);
    toggleButton_ = new QPushButton(tr("启用/禁用"), this);
    closeButton_ = new QPushButton(tr("关闭"), this);

    connect(newButton_, &QPushButton::clicked, this, &PythonScriptDialog::createNewScript);
    connect(loadButton_, &QPushButton::clicked, this, &PythonScriptDialog::loadScript);
    connect(configButton_, &QPushButton::clicked, this, &PythonScriptDialog::configureSelectedScript);
    connect(toggleButton_, &QPushButton::clicked, this, &PythonScriptDialog::toggleSelectedScript);
    connect(closeButton_, &QPushButton::clicked, this, &QDialog::accept);

    buttonLayout->addWidget(newButton_);
    buttonLayout->addWidget(loadButton_);
    buttonLayout->addWidget(configButton_);
    buttonLayout->addWidget(toggleButton_);
    buttonLayout->addStretch();
    buttonLayout->addWidget(closeButton_);

    // 创建右键菜单
    contextMenu_ = new QMenu(this);
    QAction* configAction = new QAction(tr("配置"), this);
    QAction* toggleAction = new QAction(tr("启用/禁用"), this);
    QAction* reloadAction = new QAction(tr("重新加载"), this);
    QAction* removeAction = new QAction(tr("移除"), this);

    connect(configAction, &QAction::triggered, this, &PythonScriptDialog::configureSelectedScript);
    connect(toggleAction, &QAction::triggered, this, &PythonScriptDialog::toggleSelectedScript);
    connect(reloadAction, &QAction::triggered, [this]() {
        QListWidgetItem* item = scriptListWidget_->currentItem();
        if (item) {
            QString scriptName = item->data(Qt::UserRole).toString();
            try {
                std::shared_ptr<ai::plugins::PythonScriptPlugin> script = script_manager_.get_script(scriptName.toStdString());
                if (script) {
                    if (script->reload_script()) {
                        if (script) {
                            updateScriptItem(item, script);
                        }
                        QMessageBox::information(this, tr("成功"), tr("脚本重新加载成功。"));
                    } else {
                        QMessageBox::warning(this, tr("错误"), tr("脚本重新加载失败：%1").arg(QString::fromStdString(script->get_error_message())), QMessageBox::Ok);
                    }
                }
            } catch (const std::exception& e) {
                QMessageBox::warning(this, tr("错误"), tr("重新加载脚本时发生错误: %1").arg(e.what()), QMessageBox::Ok);
            }
        }
    });
    connect(removeAction, &QAction::triggered, [this]() {
        QListWidgetItem* item = scriptListWidget_->currentItem();
        if (item) {
            QString scriptName = item->data(Qt::UserRole).toString();

            QMessageBox::StandardButton reply = QMessageBox::question(
                this, tr("确认移除"),
                tr("确定要移除脚本 '%1' 吗？").arg(scriptName),
                QMessageBox::Yes | QMessageBox::No
            );

            if (reply == QMessageBox::Yes) {
                if (script_manager_.unload_script(scriptName.toStdString())) {
                    delete item;
                }
            }
        }
    });

    contextMenu_->addAction(configAction);
    contextMenu_->addAction(toggleAction);
    contextMenu_->addAction(reloadAction);
    contextMenu_->addSeparator();
    contextMenu_->addAction(removeAction);

    // 添加到主布局
    mainLayout_->addWidget(listLabel);
    mainLayout_->addWidget(scriptListWidget_);
    mainLayout_->addLayout(buttonLayout);

    setLayout(mainLayout_);
}

void PythonScriptDialog::refreshScriptList() {
    scriptListWidget_->clear();

    // 获取所有脚本
    std::vector<std::shared_ptr<ai::plugins::PythonScriptPlugin>> scripts;
    try {
        scripts = script_manager_.get_all_scripts();
    } catch (const std::exception& e) {
        QMessageBox::warning(this, tr("错误"), tr("获取脚本列表失败: %1").arg(e.what()), QMessageBox::Ok);
        return;
    }

    // 添加脚本到列表
    for (size_t i = 0; i < scripts.size(); ++i) {
        std::shared_ptr<ai::plugins::PythonScriptPlugin> script = scripts[i];
        if (script) {
            QListWidgetItem* item = createScriptItem(script);
            scriptListWidget_->addItem(item);
        }
    }
}

QListWidgetItem* PythonScriptDialog::createScriptItem(std::shared_ptr<ai::plugins::PythonScriptPlugin> script) {
    QListWidgetItem* item = new QListWidgetItem();
    updateScriptItem(item, script);
    return item;
}

void PythonScriptDialog::updateScriptItem(QListWidgetItem* item, std::shared_ptr<ai::plugins::PythonScriptPlugin> script) {
    QString scriptName = QString::fromStdString(script->get_display_name());
    QString scriptPath = QString::fromStdString(script->get_script_path());
    QString scriptDesc = QString::fromStdString(script->get_description());
    QString scriptAuthor = QString::fromStdString(script->get_author());
    QString scriptVersion = QString::fromStdString(script->get_version());

    item->setText(scriptName);
    item->setData(Qt::UserRole, scriptName);
    item->setData(Qt::UserRole + 1, scriptPath);

    QString tooltip = tr("名称: %1\n路径: %2\n描述: %3\n作者: %4\n版本: %5\n状态: %6")
                     .arg(scriptName)
                     .arg(scriptPath)
                     .arg(scriptDesc)
                     .arg(scriptAuthor)
                     .arg(scriptVersion)
                     .arg(script->is_enabled() ? tr("启用") : tr("禁用"));
    item->setToolTip(tooltip);

    if (script->is_enabled()) {
        item->setIcon(QApplication::style()->standardIcon(QStyle::SP_DialogApplyButton));
    } else {
        item->setIcon(QApplication::style()->standardIcon(QStyle::SP_DialogCancelButton));
    }
}

void PythonScriptDialog::createNewScript() {
    bool ok;
    QString scriptName = QInputDialog::getText(
        this, tr("新建脚本"),
        tr("请输入脚本名称:"), QLineEdit::Normal,
        "", &ok
    );

    if (ok && !scriptName.isEmpty()) {
        if (script_manager_.create_new_script(scriptName.toStdString())) {
            refreshScriptList();

            // 选择新创建的脚本
            for (int i = 0; i < scriptListWidget_->count(); i++) {
                QListWidgetItem* item = scriptListWidget_->item(i);
                if (item->data(Qt::UserRole).toString() == scriptName) {
                    scriptListWidget_->setCurrentItem(item);
                    break;
                }
            }

            // 打开配置对话框
            configureSelectedScript();
        } else {
            QMessageBox::warning(this, tr("错误"), tr("创建脚本失败。"), QMessageBox::Ok);
        }
    }
}

void PythonScriptDialog::loadScript() {
    QString scriptDir = QString::fromStdString(script_manager_.get_script_directory());
    QString scriptPath = QFileDialog::getOpenFileName(
        this, tr("加载 Python 脚本"),
        scriptDir,
        tr("Python 脚本 (*.py)")
    );

    if (!scriptPath.isEmpty()) {
        if (script_manager_.load_script(scriptPath.toStdString())) {
            refreshScriptList();

            // 选择新加载的脚本
            QString scriptName = QFileInfo(scriptPath).baseName();
            for (int i = 0; i < scriptListWidget_->count(); i++) {
                QListWidgetItem* item = scriptListWidget_->item(i);
                if (item->data(Qt::UserRole).toString() == scriptName) {
                    scriptListWidget_->setCurrentItem(item);
                    break;
                }
            }
        } else {
            QMessageBox::warning(this, tr("错误"), tr("加载脚本失败。"));
        }
    }
}

void PythonScriptDialog::configureSelectedScript() {
    QListWidgetItem* item = scriptListWidget_->currentItem();
    if (!item) {
        QMessageBox::information(this, tr("提示"), tr("请先选择一个脚本。"));
        return;
    }

    QString scriptName = item->data(Qt::UserRole).toString();
    std::shared_ptr<ai::plugins::PythonScriptPlugin> script;

    try {
        script = script_manager_.get_script(scriptName.toStdString());
    } catch (const std::exception& e) {
        QMessageBox::warning(this, tr("错误"), tr("获取脚本时发生错误: %1").arg(e.what()), QMessageBox::Ok);
        return;
    }

    if (script) {
        QWidget* configWidget;
        if (configWidget) {
            QDialog dialog(this);
            dialog.setWindowTitle(tr("配置脚本 - %1").arg(scriptName));
            dialog.setMinimumSize(800, 600);

            QVBoxLayout* layout = new QVBoxLayout(&dialog);
            layout->addWidget(configWidget);

            QHBoxLayout* buttonLayout = new QHBoxLayout();
            QPushButton* closeButton = new QPushButton(tr("关闭"), &dialog);
            connect(closeButton, &QPushButton::clicked, &dialog, &QDialog::accept);

            buttonLayout->addStretch();
            buttonLayout->addWidget(closeButton);

            layout->addLayout(buttonLayout);

            dialog.setLayout(layout);
            dialog.exec();

            // 更新列表项
            if (script) {
                updateScriptItem(item, script);
            }
        }
    }
}

void PythonScriptDialog::toggleSelectedScript() {
    QListWidgetItem* item = scriptListWidget_->currentItem();
    if (!item) {
        QMessageBox::information(this, tr("提示"), tr("请先选择一个脚本。"));
        return;
    }

    QString scriptName = item->data(Qt::UserRole).toString();
    std::shared_ptr<ai::plugins::PythonScriptPlugin> script;

    try {
        script = script_manager_.get_script(scriptName.toStdString());
    } catch (const std::exception& e) {
        QMessageBox::warning(this, tr("错误"), tr("获取脚本时发生错误: %1").arg(e.what()), QMessageBox::Ok);
        return;
    }

    if (script) {
        bool new_state = !script->is_enabled();
        script->set_enabled(new_state);

        // 如果脚本被启用，确保它已初始化
        if (new_state) {
            if (!script->initialize()) {
                QMessageBox::warning(this, tr("警告"),
                    tr("脚本初始化失败: %1").arg(QString::fromStdString(script->get_error_message())), QMessageBox::Ok);
                script->set_enabled(false);
                new_state = false;
            }
            // 将脚本注册到插件管理器
            // 直接将脚本注册为处理器的插件
            if (processor_) {
                processor_->register_plugin(script);
            }
        }

        if (script) {
            updateScriptItem(item, script);
        }
    }
}

void PythonScriptDialog::onScriptItemDoubleClicked(QListWidgetItem* item) {
    configureSelectedScript();
}

void PythonScriptDialog::onScriptListContextMenu(const QPoint& pos) {
    QListWidgetItem* item = scriptListWidget_->itemAt(pos);
    if (item) {
        scriptListWidget_->setCurrentItem(item);
        contextMenu_->exec(scriptListWidget_->mapToGlobal(pos));
    }
}

} // namespace ui

