#include "ui/fullscreen_window.h"

#include <QVBoxLayout>
#include <QGraphicsPixmapItem>
#include "utils/q_plugin_renderer.h"

namespace ui {

FullscreenVideoWindow::FullscreenVideoWindow(QWidget* parent) : QWidget(parent), hasResult(false) {
    // 初始化全屏窗口
    setWindowFlags(Qt::Window | Qt::FramelessWindowHint);
    setAttribute(Qt::WA_DeleteOnClose);

    // 创建布局
    QVBoxLayout* layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setSpacing(0);

    // 创建视频显示视图
    videoView = new QGraphicsView(this);
    videoView->setRenderHint(QPainter::Antialiasing);
    videoView->setRenderHint(QPainter::SmoothPixmapTransform);
    videoView->setRenderHint(QPainter::TextAntialiasing);
    videoView->setViewportUpdateMode(QGraphicsView::FullViewportUpdate);
    videoView->setOptimizationFlags(QGraphicsView::DontSavePainterState);
    videoView->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    videoView->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    videoView->setFrameShape(QFrame::NoFrame);
    videoView->setStyleSheet("background-color: black;");
    videoView->setAlignment(Qt::AlignCenter);

    // 创建初始场景
    QGraphicsScene* initialScene = new QGraphicsScene(this);
    videoView->setScene(initialScene);

    layout->addWidget(videoView);

    // 初始隐藏
    hide();
}

FullscreenVideoWindow::~FullscreenVideoWindow() {
    // 清理资源
    if (videoView && videoView->scene()) {
        delete videoView->scene();
    }
}

void FullscreenVideoWindow::updateFrame(const QPixmap& pixmap) {
    if (!isVisible()) return;

    // 兼容旧接口，创建新场景并显示图像
    QGraphicsScene* scene = new QGraphicsScene(this);
    QGraphicsPixmapItem* pixmapItem = scene->addPixmap(pixmap);
    pixmapItem->setZValue(0);

    // 设置场景到视图
    if (videoView->scene()) {
        delete videoView->scene();
    }
    videoView->setScene(scene);
    videoView->setSceneRect(scene->sceneRect());
    videoView->fitInView(scene->sceneRect(), Qt::KeepAspectRatio);

    // 清除当前帧和结果
    currentFrame = cv::Mat();
    currentResult = ai::FrameResult();
    hasResult = false;
}

void FullscreenVideoWindow::updateFrameWithResult(const cv::Mat& frame, const ai::FrameResult& result) {
    if (!isVisible() || frame.empty()) return;

    // 保存当前帧和结果
    currentFrame = frame.clone();
    currentResult = result;
    hasResult = !result.ext_info.empty() || !result.detection_tracks.empty();

    // 创建新场景
    QGraphicsScene* scene = new QGraphicsScene(this);

    // 将OpenCV图像转换为QImage并添加到场景
    QImage image = utils::QPluginRenderer::mat_to_qimage(currentFrame);
    if (!image.isNull()) {
        QGraphicsPixmapItem* pixmapItem = scene->addPixmap(QPixmap::fromImage(image));
        pixmapItem->setZValue(0);

        // 设置场景到视图
        if (videoView->scene()) {
            delete videoView->scene();
        }
        videoView->setScene(scene);
        videoView->setSceneRect(scene->sceneRect());
        videoView->fitInView(scene->sceneRect(), Qt::KeepAspectRatio);

        // 如果有渲染信息或检测跟踪结果，在图像上方渲染图形元素
        if (hasResult) {
            QSize frameSize(currentFrame.cols, currentFrame.rows);
            utils::QPluginRenderer pluginRenderer;

            // 如果有渲染信息，直接使用
            if (!result.ext_info.empty()) {
                pluginRenderer.render_graphics_frame_result_to_view(videoView, frameSize, currentResult);
            }
            // 如果没有渲染信息但有检测跟踪结果，手动创建基本渲染信息
            else if (!result.detection_tracks.empty()) {
                // 创建一个基本的渲染信息JSON
                Json::Value render_info;
                render_info["should_render"] = true;

                // 添加跟踪目标
                Json::Value tracks_array(Json::arrayValue);
                for (const auto& track : result.detection_tracks) {
                    Json::Value track_obj;
                    track_obj["track_id"] = track.track_id;
                    track_obj["class"] = track.detect_class;
                    track_obj["score"] = track.score;

                    // 添加边界框
                    Json::Value bbox(Json::arrayValue);
                    bbox.append(track.tlwh.x);  // x
                    bbox.append(track.tlwh.y);  // y
                    bbox.append(track.tlwh.width);  // width
                    bbox.append(track.tlwh.height);  // height
                    track_obj["bbox"] = bbox;

                    tracks_array.append(track_obj);
                }
                render_info["tracks"] = tracks_array;

                // 渲染到视图
                pluginRenderer.render_graphics_to_view(videoView, frameSize, render_info);
            }
        }
    } else {
        delete scene;
    }
}

void FullscreenVideoWindow::keyPressEvent(QKeyEvent* event) {
    if (event->key() == Qt::Key_Escape) {
        // ESC键退出全屏
        close();
        event->accept();
    } else {
        QWidget::keyPressEvent(event);
    }
}

void FullscreenVideoWindow::mouseDoubleClickEvent(QMouseEvent* event) {
    // 双击也退出全屏
    close();
    event->accept();
}

void FullscreenVideoWindow::resizeEvent(QResizeEvent* event) {
    QWidget::resizeEvent(event);

    // 窗口大小变化时，重新调整视图
    if (videoView && videoView->scene()) {
        videoView->fitInView(videoView->scene()->sceneRect(), Qt::KeepAspectRatio);
    }

    // 如果有当前帧和结果，重新渲染
    if (!currentFrame.empty()) {
        updateFrameWithResult(currentFrame, currentResult);
    }
}

} // namespace ui
