#pragma once

#include <QMessageBox>
#include <QString>
#include <QWidget>

namespace utils {

/**
 * @brief 显示可滚动的消息对话框
 * @param parent 父窗口
 * @param title 对话框标题
 * @param text 对话框内容
 * @param icon 对话框图标
 */
 void showScrollableMessageBox(QWidget* parent, const QString& title,
                             const QString& text,
                             QMessageBox::Icon icon = QMessageBox::Warning);

} // namespace utils
