#include "ai/plugins/ui/cpp_frame_processor_dialog.h"
#include "utils/settings_manager.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QHeaderView>
#include <QMessageBox>
#include <algorithm> // 用于 std::transform

namespace ai {
namespace plugins {
namespace ui {

CppFrameProcessorDialog::CppFrameProcessorDialog(std::shared_ptr<ai::CppFrameProcessor> processor, QWidget* parent)
    : QDialog(parent), processor_(processor), isEnabled_(false) {
    // 打印调试信息
    std::cout << "Creating CppFrameProcessorDialog, current plugin name: " <<
        (processor ? processor->get_current_plugin_name() : "<null>") << std::endl;
    setWindowTitle(tr("C++ 帧处理器设置"));
    setMinimumSize(500, 400);

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // 创建插件选择组
    QGroupBox* pluginGroupBox = new QGroupBox(tr("插件选择"), this);
    QVBoxLayout* pluginLayout = new QVBoxLayout(pluginGroupBox);

    // 创建插件选择下拉框
    QHBoxLayout* pluginSelectionLayout = new QHBoxLayout();
    QLabel* pluginLabel = new QLabel(tr("选择插件:"), this);
    pluginComboBox_ = new QComboBox(this);
    pluginComboBox_->setMinimumWidth(300);

    pluginSelectionLayout->addWidget(pluginLabel);
    pluginSelectionLayout->addWidget(pluginComboBox_);
    pluginSelectionLayout->addStretch();

    pluginLayout->addLayout(pluginSelectionLayout);

    // 创建启用复选框
    enabledCheckBox_ = new QCheckBox(tr("启用帧处理"), this);
    pluginLayout->addWidget(enabledCheckBox_);

    mainLayout->addWidget(pluginGroupBox);

    // 创建插件信息组
    QGroupBox* infoGroupBox = new QGroupBox(tr("插件信息"), this);
    QVBoxLayout* infoLayout = new QVBoxLayout(infoGroupBox);

    descriptionLabel_ = new QLabel(this);
    versionLabel_ = new QLabel(this);
    authorLabel_ = new QLabel(this);
    requiredFramesLabel_ = new QLabel(this);

    infoLayout->addWidget(descriptionLabel_);
    infoLayout->addWidget(versionLabel_);
    infoLayout->addWidget(authorLabel_);
    infoLayout->addWidget(requiredFramesLabel_);

    mainLayout->addWidget(infoGroupBox);

    // 创建参数组
    QGroupBox* parametersGroupBox = new QGroupBox(tr("参数设置"), this);
    QVBoxLayout* parametersLayout = new QVBoxLayout(parametersGroupBox);

    parametersTable_ = new QTableWidget(0, 2, this);
    parametersTable_->setHorizontalHeaderLabels({tr("参数名"), tr("值")});
    parametersTable_->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Stretch);
    parametersTable_->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Stretch);
    parametersTable_->verticalHeader()->setVisible(false);

    parametersLayout->addWidget(parametersTable_);

    mainLayout->addWidget(parametersGroupBox);

    // 创建按钮布局
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    applyButton_ = new QPushButton(tr("应用"), this);
    cancelButton_ = new QPushButton(tr("取消"), this);

    buttonLayout->addStretch();
    buttonLayout->addWidget(applyButton_);
    buttonLayout->addWidget(cancelButton_);

    mainLayout->addLayout(buttonLayout);

    // 连接信号和槽
    connect(pluginComboBox_, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &CppFrameProcessorDialog::onPluginSelectionChanged);
    connect(enabledCheckBox_, &QCheckBox::stateChanged, this, &CppFrameProcessorDialog::onEnabledStateChanged);
    connect(parametersTable_, &QTableWidget::cellChanged, this, &CppFrameProcessorDialog::onParameterValueChanged);
    connect(applyButton_, &QPushButton::clicked, this, &CppFrameProcessorDialog::applySettings);
    connect(cancelButton_, &QPushButton::clicked, this, &CppFrameProcessorDialog::cancelSettings);

    // 初始化界面
    if (processor_) {
        // 获取当前设置
        isEnabled_ = processor_->is_enabled();
        std::cout << "Dialog: processor is_enabled: " << (isEnabled_ ? "true" : "false") << std::endl;

        currentPluginName_ = processor_->get_current_plugin_name();
        std::cout << "Dialog: processor current_plugin_name: '" << currentPluginName_ << "'" << std::endl;

        currentParams_ = processor_->get_params();
        std::cout << "Dialog: processor params count: " << currentParams_.size() << std::endl;

        // 设置启用状态
        enabledCheckBox_->setChecked(isEnabled_);

        // 填充插件列表
        auto pluginNames = processor_->get_plugin_names();
        std::cout << "Dialog: available plugins:" << std::endl;
        for (const auto& name : pluginNames) {
            std::cout << "  - '" << name << "'" << std::endl;
            pluginComboBox_->addItem(QString::fromStdString(name));
        }

        // 检查当前插件是否存在于插件列表中
        bool pluginExists = false;
        if (!currentPluginName_.empty()) {
            std::cout << "Dialog: checking if current plugin '" << currentPluginName_ << "' exists in plugin list" << std::endl;
            for (const auto& name : pluginNames) {
                if (name == currentPluginName_) {
                    pluginExists = true;
                    std::cout << "Dialog: current plugin found in plugin list" << std::endl;
                    break;
                }
            }
        }

        // 如果当前插件不存在于插件列表中，但有其他插件，则使用第一个插件
        if (!pluginExists && !pluginNames.empty()) {
            std::cout << "Dialog: current plugin not found in plugin list, using first plugin" << std::endl;
            currentPluginName_ = pluginNames[0];
            std::cout << "Dialog: set current plugin to first plugin: '" << currentPluginName_ << "'" << std::endl;
        }

        // 设置当前插件
        if (!currentPluginName_.empty()) {
            std::cout << "Dialog: setting current plugin in combo box: '" << currentPluginName_ << "'" << std::endl;
            int index = pluginComboBox_->findText(QString::fromStdString(currentPluginName_));
            std::cout << "Dialog: found index for plugin: " << index << std::endl;
            if (index >= 0) {
                pluginComboBox_->setCurrentIndex(index);
                std::cout << "Dialog: set combo box current index to: " << index << std::endl;
            } else {
                std::cout << "Dialog: plugin not found in combo box" << std::endl;
            }
        } else {
            std::cout << "Dialog: current plugin name is empty, not setting combo box" << std::endl;
        }

        // 更新插件信息和参数表格
        updatePluginInfo();
        updateParametersTable();
    }
}

void CppFrameProcessorDialog::applySettings() {
    std::cout << "Dialog: applying settings..." << std::endl;
    if (!processor_) {
        std::cout << "Dialog: processor is null, rejecting" << std::endl;
        reject();
        return;
    }

    try {
        // 设置是否启用
        processor_->set_enabled(isEnabled_);

        // 同时设置当前插件的启用状态
        if (!currentPluginName_.empty()) {
            auto plugin = processor_->get_plugin(currentPluginName_);
            if (plugin) {
                plugin->set_enabled(isEnabled_);
            }
        }

        // 设置当前插件
        if (!currentPluginName_.empty()) {
            // 先直接设置当前插件名称，确保即使插件不存在也会保存设置
            processor_->set_current_plugin_name_directly(currentPluginName_);

            // 然后再尝试正常设置插件
            if (!processor_->set_current_plugin(currentPluginName_)) {
                // 尝试忽略大小写查找插件
                auto availablePlugins = processor_->get_plugin_names();
                for (const auto& name : availablePlugins) {
                    std::string lowerName = currentPluginName_;
                    std::string lowerPluginName = name;
                    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
                    std::transform(lowerPluginName.begin(), lowerPluginName.end(), lowerPluginName.begin(), ::tolower);

                    if (lowerName == lowerPluginName) {
                        if (processor_->set_current_plugin(name)) {
                            // 更新当前插件名称为正确的大小写
                            currentPluginName_ = name;
                            processor_->set_current_plugin_name_directly(name);
                            break;
                        }
                    }
                }
            }
        }

        // 设置参数
        processor_->set_params(currentParams_);

        // 保存配置
        processor_->save_config();

        // 强制同步设置到磁盘
        utils::SettingsManager::get_instance().sync();

        // 验证设置是否正确保存
        auto& settings = utils::SettingsManager::get_instance();
        settings.beginGroup("CppFrameProcessor");
        std::string savedPluginName = settings.value("current_plugin", "").toString().toStdString();
        settings.endGroup();

        accept();
    } catch (const std::exception& e) {
        QMessageBox::warning(this, tr("错误"), tr("应用设置时出错: %1").arg(e.what()));
    }
}

void CppFrameProcessorDialog::cancelSettings() {
    reject();
}

void CppFrameProcessorDialog::onPluginSelectionChanged(int index) {
    std::cout << "Dialog: plugin selection changed to index: " << index << std::endl;
    if (index < 0 || !processor_) {
        std::cout << "Dialog: invalid index or processor is null" << std::endl;
        return;
    }

    // 获取选中的插件名称
    std::string oldPluginName = currentPluginName_;
    currentPluginName_ = pluginComboBox_->currentText().toStdString();
    std::cout << "Dialog: plugin selection changed from '" << oldPluginName << "' to '" << currentPluginName_ << "'" << std::endl;

    // 打印所有可用的插件
    std::cout << "Dialog: available plugins from processor:" << std::endl;
    auto availablePlugins = processor_->get_plugin_names();
    for (const auto& name : availablePlugins) {
        std::cout << "  - '" << name << "'" << std::endl;
    }

    // 打印下拉框中的所有项
    std::cout << "Dialog: items in combo box:" << std::endl;
    for (int i = 0; i < pluginComboBox_->count(); ++i) {
        std::cout << "  - '" << pluginComboBox_->itemText(i).toStdString() << "'" << std::endl;
    }

    // 获取插件参数
    auto plugin = processor_->get_plugin(currentPluginName_);
    if (plugin) {
        std::cout << "Dialog: plugin found, getting params" << std::endl;
        std::cout << "Dialog: plugin name from plugin object: '" << plugin->get_name() << "'" << std::endl;
        currentParams_ = plugin->get_params();
        std::cout << "Dialog: got " << currentParams_.size() << " params" << std::endl;
    } else {
        std::cout << "Dialog: plugin not found, clearing params" << std::endl;
        std::cout << "Dialog: trying case-insensitive search..." << std::endl;

        // 尝试忽略大小写查找插件
        for (const auto& name : availablePlugins) {
            std::string lowerName = currentPluginName_;
            std::string lowerPluginName = name;
            std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
            std::transform(lowerPluginName.begin(), lowerPluginName.end(), lowerPluginName.begin(), ::tolower);

            if (lowerName == lowerPluginName) {
                std::cout << "Dialog: found plugin with case-insensitive match: '" << name << "'" << std::endl;
                plugin = processor_->get_plugin(name);
                if (plugin) {
                    std::cout << "Dialog: plugin found with case-insensitive search, getting params" << std::endl;
                    currentParams_ = plugin->get_params();
                    std::cout << "Dialog: got " << currentParams_.size() << " params" << std::endl;
                    break;
                }
            }
        }

        if (!plugin) {
            currentParams_.clear();
        }
    }

    // 更新插件信息和参数表格
    updatePluginInfo();
    updateParametersTable();
}

void CppFrameProcessorDialog::onEnabledStateChanged(int state) {
    isEnabled_ = (state == Qt::Checked);
}

void CppFrameProcessorDialog::onParameterValueChanged(int row, int column) {
    if (column != 1 || row < 0 || row >= parametersTable_->rowCount()) {
        return;
    }

    // 获取参数名和值
    QString paramName = parametersTable_->item(row, 0)->text();
    QString paramValue = parametersTable_->item(row, 1)->text();

    // 更新参数
    currentParams_[paramName.toStdString()] = paramValue.toStdString();
}

void CppFrameProcessorDialog::updateParametersTable() {
    // 断开信号连接，避免在更新表格时触发onParameterValueChanged
    disconnect(parametersTable_, &QTableWidget::cellChanged, this, &CppFrameProcessorDialog::onParameterValueChanged);

    // 清空表格
    parametersTable_->setRowCount(0);

    // 填充参数
    int row = 0;
    for (const auto& param : currentParams_) {
        parametersTable_->insertRow(row);
        parametersTable_->setItem(row, 0, new QTableWidgetItem(QString::fromStdString(param.first)));
        parametersTable_->setItem(row, 1, new QTableWidgetItem(QString::fromStdString(param.second)));
        row++;
    }

    // 重新连接信号
    connect(parametersTable_, &QTableWidget::cellChanged, this, &CppFrameProcessorDialog::onParameterValueChanged);
}

void CppFrameProcessorDialog::updatePluginInfo() {
    std::cout << "Dialog: updating plugin info for plugin: '" << currentPluginName_ << "'" << std::endl;

    if (!processor_ || currentPluginName_.empty()) {
        descriptionLabel_->setText(tr("描述: 无"));
        versionLabel_->setText(tr("版本: 无"));
        authorLabel_->setText(tr("作者: 无"));
        requiredFramesLabel_->setText(tr("需要的帧数: 无"));
        std::cout << "Dialog: processor is null or current plugin name is empty" << std::endl;
        return;
    }

    auto plugin = processor_->get_plugin(currentPluginName_);
    if (!plugin) {
        std::cout << "Dialog: plugin not found, trying case-insensitive search..." << std::endl;

        // 尝试忽略大小写查找插件
        auto availablePlugins = processor_->get_plugin_names();
        for (const auto& name : availablePlugins) {
            std::string lowerName = currentPluginName_;
            std::string lowerPluginName = name;
            std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
            std::transform(lowerPluginName.begin(), lowerPluginName.end(), lowerPluginName.begin(), ::tolower);

            if (lowerName == lowerPluginName) {
                std::cout << "Dialog: found plugin with case-insensitive match: '" << name << "'" << std::endl;
                plugin = processor_->get_plugin(name);
                if (plugin) {
                    std::cout << "Dialog: plugin found with case-insensitive search" << std::endl;
                    break;
                }
            }
        }

        if (!plugin) {
            descriptionLabel_->setText(tr("描述: 无"));
            versionLabel_->setText(tr("版本: 无"));
            authorLabel_->setText(tr("作者: 无"));
            requiredFramesLabel_->setText(tr("需要的帧数: 无"));
            std::cout << "Dialog: plugin not found even with case-insensitive search" << std::endl;
            return;
        }
    }

    descriptionLabel_->setText(tr("描述: %1").arg(QString::fromStdString(plugin->get_description())));
    versionLabel_->setText(tr("版本: %1").arg(QString::fromStdString(plugin->get_version())));
    authorLabel_->setText(tr("作者: %1").arg(QString::fromStdString(plugin->get_author())));
    requiredFramesLabel_->setText(tr("需要的帧数: %1").arg(plugin->get_required_frames()));
}

} // namespace ui
} // namespace plugins
} // namespace ai
