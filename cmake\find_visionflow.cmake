﻿
include(FetchContent)

set(lib_resource_path  https://pan.aqrose.com/f/52915e4dcdfc435781ad/?dl=1&p=VisionFlow-0.9.0-alpha.2d949d6a.M245af3-msvcrt-x64_windows-opt.tar.gz)
#set(FETCHCONTENT_FULLY_DISCONNECTED ON)

FetchContent_Declare(
  visionflow
  URL ${lib_resource_path}
  UPDATE_DISCONNECTED
)

FetchContent_GetProperties(visionflow)

if(NOT visionflow_POPULATED)
    FetchContent_Populate(visionflow)

    add_library(VisionFlow SHARED IMPORTED)
	set_target_properties(
        VisionFlow
		PROPERTIES
        IMPORTED_IMPLIB
        ${visionflow_SOURCE_DIR}/lib/visionflow.if.lib
        IMPORTED_IMPLIB_DEBUG
        ${visionflow_SOURCE_DIR}/lib/visionflow.if.lib
        IMPORTED_IMPLIB_RELEASE
        ${visionflow_SOURCE_DIR}/lib/visionflow.if.lib
		IMPORTED_LOCATION
        ${visionflow_SOURCE_DIR}/bin/visionflow.dll
		IMPORTED_LOCATION_DEBUG
        ${visionflow_SOURCE_DIR}/bin/visionflow.dll
        IMPORTED_LOCATION_RELEASE
        ${visionflow_SOURCE_DIR}/bin/visionflow.dll
		INTERFACE_INCLUDE_DIRECTORIES
        ${visionflow_SOURCE_DIR}/include
    )

    add_library(python310 SHARED IMPORTED)
	set_target_properties(
        python310
		PROPERTIES
        IMPORTED_IMPLIB
        ${visionflow_SOURCE_DIR}/lib/python310.lib
        IMPORTED_IMPLIB_DEBUG
        ${visionflow_SOURCE_DIR}/lib/python310.lib
        IMPORTED_IMPLIB_RELEASE
        ${visionflow_SOURCE_DIR}/lib/python310.lib
		IMPORTED_LOCATION
        ${visionflow_SOURCE_DIR}/bin/python310.dll
		IMPORTED_LOCATION_DEBUG
        ${visionflow_SOURCE_DIR}/bin/python310.dll
        IMPORTED_LOCATION_RELEASE
        ${visionflow_SOURCE_DIR}/bin/python310.dll
		INTERFACE_INCLUDE_DIRECTORIES
        ${visionflow_SOURCE_DIR}/include
    )

    add_library(python3 SHARED IMPORTED)
	set_target_properties(
        python3
		PROPERTIES
        IMPORTED_IMPLIB
        ${visionflow_SOURCE_DIR}/lib/python3.lib
        IMPORTED_IMPLIB_DEBUG
        ${visionflow_SOURCE_DIR}/lib/python3.lib
        IMPORTED_IMPLIB_RELEASE
        ${visionflow_SOURCE_DIR}/lib/python3.lib
		IMPORTED_LOCATION
        ${visionflow_SOURCE_DIR}/bin/python3.dll
		IMPORTED_LOCATION_DEBUG
        ${visionflow_SOURCE_DIR}/bin/python3.dll
        IMPORTED_LOCATION_RELEASE
        ${visionflow_SOURCE_DIR}/bin/python3.dll
		INTERFACE_INCLUDE_DIRECTORIES
        ${visionflow_SOURCE_DIR}/include
    )

    install(
        DIRECTORY
        ${visionflow_SOURCE_DIR}/bin/
        DESTINATION release
        FILES_MATCHING PATTERN "*.*"
    )
endif()

